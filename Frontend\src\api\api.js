// Comprehensive API Configuration for <PERSON><PERSON>min System
// This file contains all API endpoints organized by modules

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

// API Endpoints Configuration
export const API_ENDPOINTS = {
  // Base URL
  BASE_URL: API_BASE_URL,

  // Health Check
  HEALTH: "/api/health",

  // Authentication APIs
  AUTH: {
    LOGIN_ADMIN: "/api/auth/login-admin",
    LOGIN_EMPLOYEE: "/api/auth/login-employee",
    VERIFY_CODE: "/api/auth/verify-code",
    REGISTER: "/api/auth/register",
    FORGOT_PASSWORD: "/api/auth/forgot-password",
    RESET_PASSWORD: "/api/auth/reset-password",
    SET_STAFF_PASSWORD: "/api/auth/staff/set-password",
    VERIFY_EMAIL: "/api/auth/verify-email",
    REFRESH_TOKEN: "/api/auth/refresh-token",
    LOGOUT: "/api/auth/logout",
  },

  // User Management APIs
  USERS: {
    GET_ALL: "/api/user/users",
    GET_BY_ID: (id) => `/api/user/users/${id}`,
    CREATE: "/api/user/users",
    UPDATE: (id) => `/api/user/users/${id}`,
    DELETE: (id) => `/api/user/users/${id}`,
    UPDATE_STATUS: (id) => `/api/user/users/${id}/status`,
    GET_PROFILE: "/api/user/users/profile",
    UPDATE_PROFILE: "/api/user/users/profile",
    CHANGE_PASSWORD: "/api/user/users/change-password",
  },

  // Admin User Management APIs
  ADMIN_USERS: {
    GET_ALL: "/api/admin/users",
    GET_BY_ID: (id) => `/api/admin/users/${id}`,
    CREATE: "/api/admin/users",
    UPDATE: (id) => `/api/admin/users/${id}`,
    DELETE: (id) => `/api/admin/users/${id}`,
    UPDATE_STATUS: (id) => `/api/admin/users/${id}/status`,
    GET_STATS: "/api/admin/users/stats",
  },

  // Product Management APIs
  PRODUCTS: {
    GET_ALL: "/api/admin/products",
    GET_BY_ID: (id) => `/api/admin/products/${id}`,
    CREATE: "/api/admin/products",
    UPDATE: (id) => `/api/admin/products/${id}`,
    DELETE: (id) => `/api/admin/products/${id}`,
    TOGGLE_FEATURED: (id) => `/api/admin/products/${id}/featured`,
    TOGGLE_STOCK: (id) => `/api/admin/products/${id}/stock`,
    GET_STATS: "/api/admin/products/stats",
    UPLOAD_IMAGES: (id) => `/api/admin/products/${id}/images`,
    GET_FEATURED: "/api/user/products/featured",
    SEARCH: "/api/user/products/search",
  },

  // Category Management APIs
  CATEGORIES: {
    GET_ALL: "/api/admin/categories",
    GET_BY_ID: (id) => `/api/admin/categories/${id}`,
    CREATE: "/api/admin/categories",
    UPDATE: (id) => `/api/admin/categories/${id}`,
    DELETE: (id) => `/api/admin/categories/${id}`,
    GET_STATS: "/api/admin/categories/stats",
    GET_PRODUCTS: "/api/admin/categories/category-products",
  },

  // Order Management APIs
  ORDERS: {
    GET_ALL: "/api/admin/orders",
    GET_BY_ID: (id) => `/api/admin/orders/${id}`,
    CREATE: "/api/user/orders",
    UPDATE: (id) => `/api/admin/orders/${id}`,
    DELETE: (id) => `/api/admin/orders/${id}`,
    UPDATE_STATUS: (id) => `/api/admin/orders/${id}/status`,
    GET_STATS: "/api/admin/orders/stats",
    GET_USER_ORDERS: "/api/user/orders",
    GET_USER_ORDER: (id) => `/api/user/orders/${id}`,
  },

  // Inventory Management APIs
  INVENTORY: {
    GET_ALL: "/api/admin/inventory",
    GET_BY_ID: (id) => `/api/admin/inventory/${id}`,
    UPDATE: (id) => `/api/admin/inventory/${id}`,
    GET_LOW_STOCK: "/api/admin/inventory/low-stock",
    GET_STATS: "/api/admin/inventory/stats",
    BULK_UPDATE: "/api/admin/inventory/bulk-update",
  },

  // Customer Management APIs
  CUSTOMERS: {
    GET_ALL: "/api/admin/customers",
    GET_BY_ID: (id) => `/api/admin/customers/${id}`,
    UPDATE: (id) => `/api/admin/customers/${id}`,
    DELETE: (id) => `/api/admin/customers/${id}`,
    GET_STATS: "/api/admin/customers/stats",
    GET_ORDERS: (id) => `/api/admin/customers/${id}/orders`,
  },

  // Patient Management APIs
  PATIENTS: {
    GET_ALL: "/api/admin/patients",
    GET_BY_ID: (id) => `/api/admin/patients/${id}`,
    CREATE: "/api/admin/patients",
    UPDATE: (id) => `/api/admin/patients/${id}`,
    DELETE: (id) => `/api/admin/patients/${id}`,
    GET_STATS: "/api/admin/patients/stats",
  },

  // Complaints & Feedback APIs
  COMPLAINTS: {
    GET_ALL: "/api/admin/complaints",
    GET_BY_ID: (id) => `/api/admin/complaints/${id}`,
    CREATE: "/api/user/complaints",
    UPDATE: (id) => `/api/admin/complaints/${id}`,
    DELETE: (id) => `/api/admin/complaints/${id}`,
    UPDATE_STATUS: (id) => `/api/admin/complaints/${id}/status`,
    GET_STATS: "/api/admin/complaints/stats",
  },

  // Returns Management APIs
  RETURNS: {
    GET_ALL: "/api/admin/returns",
    GET_BY_ID: (id) => `/api/admin/returns/${id}`,
    CREATE: "/api/user/returns",
    UPDATE: (id) => `/api/admin/returns/${id}`,
    DELETE: (id) => `/api/admin/returns/${id}`,
    UPDATE_STATUS: (id) => `/api/admin/returns/${id}/status`,
    GET_STATS: "/api/admin/returns/stats",
  },

  // Staff Management APIs
  STAFF: {
    GET_ALL: "/api/admin/staff",
    GET_BY_ID: (id) => `/api/admin/staff/${id}`,
    CREATE: "/api/admin/staff",
    UPDATE: (id) => `/api/admin/staff/${id}`,
    DELETE: (id) => `/api/admin/staff/${id}`,
    UPDATE_STATUS: (id) => `/api/admin/staff/${id}/status`,
  },

  // Analytics APIs
  ANALYTICS: {
    DASHBOARD: "/api/admin/analytics/dashboard",
    SALES: "/api/admin/analytics/sales",
    PRODUCTS: "/api/admin/analytics/products",
    CUSTOMERS: "/api/admin/analytics/customers",
    REVENUE: "/api/admin/analytics/revenue",
    ORDERS: "/api/admin/analytics/orders",
    INVENTORY: "/api/admin/analytics/inventory",
  },

  // Notification APIs
  NOTIFICATIONS: {
    GET_ALL: "/api/user/notifications",
    GET_BY_ID: (id) => `/api/user/notifications/${id}`,
    MARK_READ: (id) => `/api/user/notifications/${id}/read`,
    MARK_ALL_READ: "/api/user/notifications/mark-all-read",
    DELETE: (id) => `/api/user/notifications/${id}`,
    GET_UNREAD_COUNT: "/api/user/notifications/unread-count",
  },

  // Coupon & Discount APIs
  COUPONS: {
    GET_ALL: "/api/admin/coupons",
    GET_BY_ID: (id) => `/api/admin/coupons/${id}`,
    CREATE: "/api/admin/coupons",
    UPDATE: (id) => `/api/admin/coupons/${id}`,
    DELETE: (id) => `/api/admin/coupons/${id}`,
    TOGGLE_STATUS: (id) => `/api/admin/coupons/${id}/status`,
    VALIDATE: "/api/user/coupons/validate",
  },

  // Review Management APIs
  REVIEWS: {
    GET_ALL: "/api/admin/reviews",
    GET_BY_ID: (id) => `/api/admin/reviews/${id}`,
    UPDATE_STATUS: (id) => `/api/admin/reviews/${id}/status`,
    DELETE: (id) => `/api/admin/reviews/${id}`,
    GET_PRODUCT_REVIEWS: (productId) =>
      `/api/user/products/${productId}/reviews`,
    CREATE_REVIEW: (productId) => `/api/user/products/${productId}/reviews`,
  },

  // Banner Management APIs
  BANNERS: {
    GET_ALL: "/api/admin/banners",
    GET_BY_ID: (id) => `/api/admin/banners/${id}`,
    CREATE: "/api/admin/banners",
    UPDATE: (id) => `/api/admin/banners/${id}`,
    DELETE: (id) => `/api/admin/banners/${id}`,
    TOGGLE_STATUS: (id) => `/api/admin/banners/${id}/status`,
    GET_ACTIVE: "/api/user/banners/active",
  },

  // Cart Management APIs
  CART: {
    GET: "/api/user/cart",
    ADD_ITEM: "/api/user/cart/add",
    UPDATE_ITEM: "/api/user/cart/update",
    REMOVE_ITEM: "/api/user/cart/remove",
    CLEAR: "/api/user/cart/clear",
    GET_COUNT: "/api/user/cart/count",
  },

  // File Upload APIs
  UPLOAD: {
    IMAGE: "/api/upload/image",
    IMAGES: "/api/upload/images",
    DOCUMENT: "/api/upload/document",
    AVATAR: "/api/upload/avatar",
  },

  // Settings APIs
  SETTINGS: {
    GET_ALL: "/api/admin/settings",
    UPDATE: "/api/admin/settings",
    GET_PUBLIC: "/api/settings/public",
  },
};

// HTTP Methods
export const HTTP_METHODS = {
  GET: "GET",
  POST: "POST",
  PUT: "PUT",
  DELETE: "DELETE",
  PATCH: "PATCH",
};

// Request timeout in milliseconds
export const REQUEST_TIMEOUT = 30000;

// Default headers
export const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
  Accept: "application/json",
};

export default API_ENDPOINTS;
