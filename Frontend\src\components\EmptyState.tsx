import React from "react";
import { LucideIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
  size?: "sm" | "md" | "lg";
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon,
  title,
  description,
  actionLabel,
  onAction,
  className = "",
  size = "md",
}) => {
  const sizeClasses = {
    sm: {
      container: "py-8",
      icon: "w-12 h-12",
      title: "text-lg",
      description: "text-sm",
    },
    md: {
      container: "py-12",
      icon: "w-16 h-16",
      title: "text-xl",
      description: "text-base",
    },
    lg: {
      container: "py-16",
      icon: "w-20 h-20",
      title: "text-2xl",
      description: "text-lg",
    },
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={`text-center ${currentSize.container} ${className}`}>
      {Icon && (
        <div className="flex justify-center mb-4">
          <Icon className={`${currentSize.icon} text-gray-400 dark:text-gray-500`} />
        </div>
      )}
      
      <h3 className={`${currentSize.title} font-medium text-gray-900 dark:text-white mb-2`}>
        {title}
      </h3>
      
      <p className={`${currentSize.description} text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto`}>
        {description}
      </p>
      
      {actionLabel && onAction && (
        <Button
          onClick={onAction}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {actionLabel}
        </Button>
      )}
    </div>
  );
};

export default EmptyState;
