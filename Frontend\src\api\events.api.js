// Events/Banners API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class EventsAPI {
  // Get all banners with optional filters
  async getEvents(filters = {}) {
    try {
      const response = await serviceWorkerAPI.getBanners(filters);
      console.log("Banners fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch banners:", error);
      throw error;
    }
  }

  // Get banner by ID
  async getEventById(id) {
    try {
      const response = await serviceWorkerAPI.getBannerById(id);
      console.log("Banner fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch banner:", error);
      throw error;
    }
  }

  // Create new banner
  async createEvent(eventData) {
    try {
      const response = await serviceWorkerAPI.createBanner(eventData);
      console.log("Banner created successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to create banner:", error);
      throw error;
    }
  }

  // Update existing banner
  async updateEvent(id, eventData) {
    try {
      const response = await serviceWorkerAPI.updateBanner(id, eventData);
      console.log("Banner updated successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to update banner:", error);
      throw error;
    }
  }

  // Delete banner
  async deleteEvent(id) {
    try {
      const response = await serviceWorkerAPI.deleteBanner(id);
      console.log("Banner deleted successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to delete banner:", error);
      throw error;
    }
  }

  // Format event for display
  formatEventForDisplay(event) {
    return {
      id: event._id || event.id,
      title: event.title || "",
      description: event.description || "",
      date: new Date(event.date) || new Date(),
      imageUrl: event.imageUrl || "",
      priority: event.priority || "Level 2",
      link: event.link || "",
      validFrom: new Date(event.validFrom) || new Date(),
      validTill: new Date(event.validTill) || new Date(),
      createdAt: event.createdAt ? new Date(event.createdAt) : new Date(),
      updatedAt: event.updatedAt ? new Date(event.updatedAt) : null,
    };
  }
}

const eventsAPI = new EventsAPI();
export default eventsAPI;
