// Service Worker API - Handles all API calls and returns data in JSON format
import {
  API_ENDPOINTS,
  HTTP_METHODS,
  REQUEST_TIMEOUT,
  DEFAULT_HEADERS,
} from "./api.js";

class ServiceWorkerAPI {
  constructor() {
    this.baseURL = API_ENDPOINTS.BASE_URL;
    this.timeout = REQUEST_TIMEOUT;
    this.defaultHeaders = DEFAULT_HEADERS;
  }

  // Get authentication token from localStorage or sessionStorage
  getAuthToken() {
    return (
      localStorage.getItem("auth-token") || sessionStorage.getItem("auth-token")
    );
  }

  // Build headers with authentication
  buildHeaders(customHeaders = {}) {
    const headers = { ...this.defaultHeaders, ...customHeaders };

    const token = this.getAuthToken();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    return headers;
  }

  // Build URL with query parameters
  buildURL(endpoint, params = {}) {
    const url = new URL(endpoint, this.baseURL);

    Object.keys(params).forEach((key) => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    return url.toString();
  }

  // Handle API response
  async handleResponse(response) {
    const contentType = response.headers.get("content-type");

    let data;
    if (contentType && contentType.includes("application/json")) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      // Handle different error status codes
      const error = {
        status: response.status,
        statusText: response.statusText,
        message: data.message || data || "An error occurred",
        data: data,
      };

      // Handle token expiration
      if (response.status === 401) {
        this.handleTokenExpiration();
      }

      throw error;
    }

    return data;
  }

  // Handle token expiration
  handleTokenExpiration() {
    localStorage.removeItem("auth-token");
    localStorage.removeItem("auth-user");
    localStorage.removeItem("keep-logged-in");
    sessionStorage.removeItem("auth-token");
    sessionStorage.removeItem("auth-user");
    sessionStorage.removeItem("keep-logged-in");

    // Redirect to login page
    if (typeof window !== "undefined") {
      window.location.href = "/auth/signin";
    }
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const {
      method = HTTP_METHODS.GET,
      headers = {},
      body = null,
      params = {},
      timeout = this.timeout,
    } = options;

    const url = this.buildURL(endpoint, params);
    const requestHeaders = this.buildHeaders(headers);

    const config = {
      method,
      headers: requestHeaders,
      signal: AbortSignal.timeout(timeout),
    };

    // Add body for non-GET requests
    if (body && method !== HTTP_METHODS.GET) {
      if (body instanceof FormData) {
        // Remove Content-Type header for FormData (browser will set it with boundary)
        delete config.headers["Content-Type"];
        config.body = body;
      } else if (typeof body === "object") {
        config.body = JSON.stringify(body);
      } else {
        config.body = body;
      }
    }

    try {
      const response = await fetch(url, config);
      return await this.handleResponse(response);
    } catch (error) {
      if (error.name === "AbortError") {
        throw new Error("Request timeout");
      }
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}, headers = {}) {
    return this.request(endpoint, {
      method: HTTP_METHODS.GET,
      params,
      headers,
    });
  }

  // POST request
  async post(endpoint, body = null, headers = {}) {
    return this.request(endpoint, {
      method: HTTP_METHODS.POST,
      body,
      headers,
    });
  }

  // PUT request
  async put(endpoint, body = null, headers = {}) {
    return this.request(endpoint, {
      method: HTTP_METHODS.PUT,
      body,
      headers,
    });
  }

  // PATCH request
  async patch(endpoint, body = null, headers = {}) {
    return this.request(endpoint, {
      method: HTTP_METHODS.PATCH,
      body,
      headers,
    });
  }

  // DELETE request
  async delete(endpoint, headers = {}) {
    return this.request(endpoint, {
      method: HTTP_METHODS.DELETE,
      headers,
    });
  }

  // File upload request
  async uploadFile(endpoint, formData, headers = {}) {
    return this.request(endpoint, {
      method: HTTP_METHODS.POST,
      body: formData,
      headers,
    });
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.get(API_ENDPOINTS.HEALTH);
      return {
        success: true,
        status: "healthy",
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        status: "unhealthy",
        error: error.message,
      };
    }
  }

  // Authentication methods
  async loginAdmin(credentials) {
    return this.post(API_ENDPOINTS.AUTH.LOGIN_ADMIN, credentials);
  }

  async loginEmployee(credentials) {
    return this.post(API_ENDPOINTS.AUTH.LOGIN_EMPLOYEE, credentials);
  }

  async verifyTwoFactor(data) {
    return this.post(API_ENDPOINTS.AUTH.VERIFY_CODE, data);
  }

  async register(userData) {
    return this.post(API_ENDPOINTS.AUTH.REGISTER, userData);
  }

  async forgotPassword(email) {
    return this.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
  }

  async resetPassword(token, password) {
    return this.post(
      API_ENDPOINTS.AUTH.RESET_PASSWORD.replace(":token", token),
      { password }
    );
  }

  // User management methods
  async getUsers(params = {}) {
    return this.get(API_ENDPOINTS.USERS.GET_ALL, params);
  }

  async getUserById(id) {
    return this.get(API_ENDPOINTS.USERS.GET_BY_ID(id));
  }

  async createUser(userData) {
    return this.post(API_ENDPOINTS.USERS.CREATE, userData);
  }

  async updateUser(id, userData) {
    return this.put(API_ENDPOINTS.USERS.UPDATE(id), userData);
  }

  async deleteUser(id) {
    return this.delete(API_ENDPOINTS.USERS.DELETE(id));
  }

  async getUserProfile() {
    return this.get(API_ENDPOINTS.USERS.GET_PROFILE);
  }

  async updateUserProfile(profileData) {
    return this.put(API_ENDPOINTS.USERS.UPDATE_PROFILE, profileData);
  }

  // Product management methods
  async getProducts(params = {}) {
    return this.get(API_ENDPOINTS.PRODUCTS.GET_ALL, params);
  }

  async getProductById(id) {
    return this.get(API_ENDPOINTS.PRODUCTS.GET_BY_ID(id));
  }

  async createProduct(productData) {
    return this.post(API_ENDPOINTS.PRODUCTS.CREATE, productData);
  }

  async updateProduct(id, productData) {
    return this.put(API_ENDPOINTS.PRODUCTS.UPDATE(id), productData);
  }

  async deleteProduct(id) {
    return this.delete(API_ENDPOINTS.PRODUCTS.DELETE(id));
  }

  async toggleProductFeatured(id) {
    return this.put(API_ENDPOINTS.PRODUCTS.TOGGLE_FEATURED(id));
  }

  async toggleProductStock(id) {
    return this.put(API_ENDPOINTS.PRODUCTS.TOGGLE_STOCK(id));
  }

  async getProductStats() {
    return this.get(API_ENDPOINTS.PRODUCTS.GET_STATS);
  }

  async getFeaturedProducts(limit) {
    return this.get(API_ENDPOINTS.PRODUCTS.GET_FEATURED, { limit });
  }

  // Category management methods
  async getCategories(params = {}) {
    return this.get(API_ENDPOINTS.CATEGORIES.GET_ALL, params);
  }

  async getCategoryById(id) {
    return this.get(API_ENDPOINTS.CATEGORIES.GET_BY_ID(id));
  }

  async createCategory(categoryData) {
    return this.post(API_ENDPOINTS.CATEGORIES.CREATE, categoryData);
  }

  async updateCategory(id, categoryData) {
    return this.put(API_ENDPOINTS.CATEGORIES.UPDATE(id), categoryData);
  }

  async deleteCategory(id) {
    return this.delete(API_ENDPOINTS.CATEGORIES.DELETE(id));
  }

  async getCategoryStats() {
    return this.get(API_ENDPOINTS.CATEGORIES.GET_STATS);
  }

  // Order management methods
  async getOrders(params = {}) {
    return this.get(API_ENDPOINTS.ORDERS.GET_ALL, params);
  }

  async getOrderById(id) {
    return this.get(API_ENDPOINTS.ORDERS.GET_BY_ID(id));
  }

  async createOrder(orderData) {
    return this.post(API_ENDPOINTS.ORDERS.CREATE, orderData);
  }

  async updateOrder(id, orderData) {
    return this.put(API_ENDPOINTS.ORDERS.UPDATE(id), orderData);
  }

  async updateOrderStatus(id, status) {
    return this.put(API_ENDPOINTS.ORDERS.UPDATE_STATUS(id), { status });
  }

  async getOrderStats() {
    return this.get(API_ENDPOINTS.ORDERS.GET_STATS);
  }

  async getUserOrders(params = {}) {
    return this.get(API_ENDPOINTS.ORDERS.GET_USER_ORDERS, params);
  }

  // Customer management methods
  async getCustomers(params = {}) {
    return this.get(API_ENDPOINTS.CUSTOMERS.GET_ALL, params);
  }

  async getCustomerById(id) {
    return this.get(API_ENDPOINTS.CUSTOMERS.GET_BY_ID(id));
  }

  async updateCustomer(id, customerData) {
    return this.put(API_ENDPOINTS.CUSTOMERS.UPDATE(id), customerData);
  }

  async getCustomerStats() {
    return this.get(API_ENDPOINTS.CUSTOMERS.GET_STATS);
  }

  async getCustomerOrders(id, params = {}) {
    return this.get(API_ENDPOINTS.CUSTOMERS.GET_ORDERS(id), params);
  }

  // Returns management methods
  async getReturns(params = {}) {
    return this.get(API_ENDPOINTS.RETURNS.GET_ALL, params);
  }

  async getReturnById(id) {
    return this.get(API_ENDPOINTS.RETURNS.GET_BY_ID(id));
  }

  async createReturn(returnData) {
    return this.post(API_ENDPOINTS.RETURNS.CREATE, returnData);
  }

  async updateReturn(id, returnData) {
    return this.put(API_ENDPOINTS.RETURNS.UPDATE(id), returnData);
  }

  async updateReturnStatus(id, status) {
    return this.put(API_ENDPOINTS.RETURNS.UPDATE_STATUS(id), { status });
  }

  async getReturnStats() {
    return this.get(API_ENDPOINTS.RETURNS.GET_STATS);
  }

  // Patients management methods
  async getPatients(params = {}) {
    return this.get(API_ENDPOINTS.PATIENTS.GET_ALL, params);
  }

  async getPatientById(id) {
    return this.get(API_ENDPOINTS.PATIENTS.GET_BY_ID(id));
  }

  async createPatient(patientData) {
    return this.post(API_ENDPOINTS.PATIENTS.CREATE, patientData);
  }

  async updatePatient(id, patientData) {
    return this.put(API_ENDPOINTS.PATIENTS.UPDATE(id), patientData);
  }

  async deletePatient(id) {
    return this.delete(API_ENDPOINTS.PATIENTS.DELETE(id));
  }

  async getPatientStats() {
    return this.get(API_ENDPOINTS.PATIENTS.GET_STATS);
  }

  async schedulePatientAppointment(patientId, appointmentData) {
    return this.post(
      `/api/admin/patients/${patientId}/appointments`,
      appointmentData
    );
  }

  async addPatientHealthCondition(patientId, conditionData) {
    return this.post(
      `/api/admin/patients/${patientId}/health-conditions`,
      conditionData
    );
  }

  // Complaints management methods
  async getComplaints(params = {}) {
    return this.get(API_ENDPOINTS.COMPLAINTS.GET_ALL, params);
  }

  async getComplaintById(id) {
    return this.get(API_ENDPOINTS.COMPLAINTS.GET_BY_ID(id));
  }

  async createComplaint(complaintData) {
    return this.post(API_ENDPOINTS.COMPLAINTS.CREATE, complaintData);
  }

  async updateComplaint(id, complaintData) {
    return this.put(API_ENDPOINTS.COMPLAINTS.UPDATE(id), complaintData);
  }

  async updateComplaintStatus(id, status) {
    return this.put(API_ENDPOINTS.COMPLAINTS.UPDATE_STATUS(id), { status });
  }

  async getComplaintStats() {
    return this.get(API_ENDPOINTS.COMPLAINTS.GET_STATS);
  }

  async getUrgentComplaints(params = {}) {
    return this.get(`/api/admin/complaints/urgent`, params);
  }

  // Inventory management methods
  async getInventory(params = {}) {
    return this.get(API_ENDPOINTS.INVENTORY.GET_ALL, params);
  }

  async getInventoryById(id) {
    return this.get(API_ENDPOINTS.INVENTORY.GET_BY_ID(id));
  }

  async updateInventory(id, inventoryData) {
    return this.put(API_ENDPOINTS.INVENTORY.UPDATE(id), inventoryData);
  }

  async getLowStockItems() {
    return this.get(API_ENDPOINTS.INVENTORY.GET_LOW_STOCK);
  }

  async getInventoryStats() {
    return this.get(API_ENDPOINTS.INVENTORY.GET_STATS);
  }

  // Staff management methods
  async getStaff(params = {}) {
    return this.get(API_ENDPOINTS.STAFF.GET_ALL, params);
  }

  async getStaffById(id) {
    return this.get(API_ENDPOINTS.STAFF.GET_BY_ID(id));
  }

  async createStaff(staffData) {
    return this.post(API_ENDPOINTS.STAFF.CREATE, staffData);
  }

  async updateStaff(id, staffData) {
    return this.put(API_ENDPOINTS.STAFF.UPDATE(id), staffData);
  }

  async deleteStaff(id) {
    return this.delete(API_ENDPOINTS.STAFF.DELETE(id));
  }

  async updateStaffStatus(id, status) {
    return this.put(API_ENDPOINTS.STAFF.UPDATE_STATUS(id), { status });
  }

  // Complaints management methods
  async getComplaints(params = {}) {
    return this.get(API_ENDPOINTS.COMPLAINTS.GET_ALL, params);
  }

  async getComplaintById(id) {
    return this.get(API_ENDPOINTS.COMPLAINTS.GET_BY_ID(id));
  }

  async createComplaint(complaintData) {
    return this.post(API_ENDPOINTS.COMPLAINTS.CREATE, complaintData);
  }

  async updateComplaintStatus(id, status) {
    return this.put(API_ENDPOINTS.COMPLAINTS.UPDATE_STATUS(id), { status });
  }

  // Returns management methods
  async getReturns(params = {}) {
    return this.get(API_ENDPOINTS.RETURNS.GET_ALL, params);
  }

  async getReturnById(id) {
    return this.get(API_ENDPOINTS.RETURNS.GET_BY_ID(id));
  }

  async createReturn(returnData) {
    return this.post(API_ENDPOINTS.RETURNS.CREATE, returnData);
  }

  async updateReturnStatus(id, status) {
    return this.put(API_ENDPOINTS.RETURNS.UPDATE_STATUS(id), { status });
  }

  // Analytics methods
  async getDashboardAnalytics() {
    return this.get(API_ENDPOINTS.ANALYTICS.DASHBOARD);
  }

  async getSalesAnalytics(params = {}) {
    return this.get(API_ENDPOINTS.ANALYTICS.SALES, params);
  }

  async getProductAnalytics(params = {}) {
    return this.get(API_ENDPOINTS.ANALYTICS.PRODUCTS, params);
  }

  async getCustomerAnalytics(params = {}) {
    return this.get(API_ENDPOINTS.ANALYTICS.CUSTOMERS, params);
  }

  async getRevenueAnalytics(params = {}) {
    return this.get(API_ENDPOINTS.ANALYTICS.REVENUE, params);
  }

  // Notification methods
  async getNotifications(params = {}) {
    return this.get(API_ENDPOINTS.NOTIFICATIONS.GET_ALL, params);
  }

  async markNotificationRead(id) {
    return this.put(API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id));
  }

  async markAllNotificationsRead() {
    return this.put(API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ);
  }

  async getUnreadNotificationCount() {
    return this.get(API_ENDPOINTS.NOTIFICATIONS.GET_UNREAD_COUNT);
  }

  // Coupon methods
  async getCoupons(params = {}) {
    return this.get(API_ENDPOINTS.COUPONS.GET_ALL, params);
  }

  async createCoupon(couponData) {
    return this.post(API_ENDPOINTS.COUPONS.CREATE, couponData);
  }

  async updateCoupon(id, couponData) {
    return this.put(API_ENDPOINTS.COUPONS.UPDATE(id), couponData);
  }

  async validateCoupon(code) {
    return this.post(API_ENDPOINTS.COUPONS.VALIDATE, { code });
  }

  // Cart methods
  async getCart() {
    return this.get(API_ENDPOINTS.CART.GET);
  }

  async addToCart(productId, quantity = 1) {
    return this.post(API_ENDPOINTS.CART.ADD_ITEM, { productId, quantity });
  }

  async updateCartItem(productId, quantity) {
    return this.put(API_ENDPOINTS.CART.UPDATE_ITEM, { productId, quantity });
  }

  async removeFromCart(productId) {
    return this.delete(API_ENDPOINTS.CART.REMOVE_ITEM, { productId });
  }

  async clearCart() {
    return this.delete(API_ENDPOINTS.CART.CLEAR);
  }

  // File upload methods
  async uploadImage(file) {
    const formData = new FormData();
    formData.append("image", file);
    return this.uploadFile(API_ENDPOINTS.UPLOAD.IMAGE, formData);
  }

  async uploadImages(files) {
    const formData = new FormData();
    Array.from(files).forEach((file) => {
      formData.append("images", file);
    });
    return this.uploadFile(API_ENDPOINTS.UPLOAD.IMAGES, formData);
  }
}

// Create and export singleton instance
const serviceWorkerAPI = new ServiceWorkerAPI();
export default serviceWorkerAPI;

// Also export the class for custom instances if needed
export { ServiceWorkerAPI };

// Import and export individual API modules
import complaintsAPI from "./complaints.api";
import { reviewsAPI } from "./reviews.api";

// Export API helpers for use in components
export const apiHelpers = {
  // Response helpers
  isSuccessResponse: (response) => response?.success === true,
  extractData: (response) => response?.data || response,
  formatError: (error) =>
    error?.response?.data?.message || error?.message || "An error occurred",

  // HTTP methods
  get: serviceWorkerAPI.get.bind(serviceWorkerAPI),
  post: serviceWorkerAPI.post.bind(serviceWorkerAPI),
  put: serviceWorkerAPI.put.bind(serviceWorkerAPI),
  delete: serviceWorkerAPI.delete.bind(serviceWorkerAPI),
  patch: serviceWorkerAPI.patch.bind(serviceWorkerAPI),
};

// Export specialized APIs
export { complaintsAPI, reviewsAPI };
