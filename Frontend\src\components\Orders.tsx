import { useState, use<PERSON>emo, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Eye,
  Trash2,
  Search,
  RefreshCw,
  Download,
  AlertCircle,
  Package,
  Loader2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import EnhancedOrderViewModal from "./EnhancedOrderViewModal";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import ordersAPI from "@/api/orders.api.js";
import { apiHelpers } from "@/api";

// Interface for order data
interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface Customer {
  name: string;
  email: string;
  phone?: string;
}

interface Order {
  id: string;
  orderNumber: string;
  customer: Customer;
  date: string;
  items: OrderItem[];
  amount: string;
  status: string;
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  paymentStatus?: string;
  paymentMethod?: string;
  trackingNumber?: string;
  shippingAddress?: any;
  createdAt: string;
  updatedAt?: string;
}

const Orders = () => {
  const { toast } = useToast();

  // State management
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isOrderViewModalOpen, setIsOrderViewModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Statistics state
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0,
    cancelled: 0,
  });

  // Fetch orders from API
  const fetchOrders = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const filters = {
        search: searchTerm,
        status: statusFilter !== "all" ? statusFilter : "",
        limit: 100, // Get all orders for now
      };

      const response = await ordersAPI.getOrders(filters);

      if (apiHelpers.isSuccessResponse(response)) {
        const data = apiHelpers.extractData(response);
        const ordersData = data.orders || [];

        // Format orders for display
        const formattedOrders = ordersData.map((order: any) =>
          ordersAPI.formatOrderForDisplay(order)
        );

        setOrders(formattedOrders);

        // Calculate statistics
        const newStats = {
          total: formattedOrders.length,
          pending: formattedOrders.filter((o: Order) => o.status === "pending")
            .length,
          processing: formattedOrders.filter(
            (o: Order) => o.status === "processing"
          ).length,
          shipped: formattedOrders.filter((o: Order) => o.status === "shipped")
            .length,
          delivered: formattedOrders.filter(
            (o: Order) => o.status === "delivered"
          ).length,
          cancelled: formattedOrders.filter(
            (o: Order) => o.status === "cancelled"
          ).length,
        };
        setStats(newStats);

        console.log("Orders data loaded:", formattedOrders);
      } else {
        throw new Error(response.message || "Failed to fetch orders");
      }
    } catch (error: any) {
      console.error("Failed to fetch orders:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh orders data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchOrders(false);
  };

  // Load orders data on component mount and when filters change
  useEffect(() => {
    fetchOrders();
  }, [searchTerm, statusFilter]);

  // Filter orders based on search term and filters (client-side filtering for better UX)
  const filteredOrders = useMemo(() => {
    return orders.filter((order) => {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        order.orderNumber.toLowerCase().includes(searchLower) ||
        order.customer.name.toLowerCase().includes(searchLower) ||
        order.customer.email.toLowerCase().includes(searchLower);
      const matchesStatus =
        statusFilter === "all" ||
        order.status.toLowerCase() === statusFilter.toLowerCase();

      return matchesSearch && matchesStatus;
    });
  }, [orders, searchTerm, statusFilter]);

  // Handle order deletion
  const handleDeleteOrder = async (order: Order) => {
    try {
      const response = await ordersAPI.deleteOrder(order.id);

      if (apiHelpers.isSuccessResponse(response)) {
        toast({
          title: "Order Deleted",
          description: `Order ${order.orderNumber} has been deleted`,
        });

        // Refresh orders list
        await fetchOrders(false);
      } else {
        throw new Error(response.message || "Failed to delete order");
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    }
  };

  // Handle viewing order details
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsOrderViewModalOpen(true);
  };

  // Handle order status update from modal
  const handleOrderStatusUpdate = async (
    orderId: string,
    newStatus: string
  ) => {
    // Update the order in the local state immediately for better UX
    setOrders((prevOrders) =>
      prevOrders.map((order) =>
        order.id === orderId ? { ...order, status: newStatus } : order
      )
    );

    // Update statistics
    const updatedOrders = orders.map((order) =>
      order.id === orderId ? { ...order, status: newStatus } : order
    );

    const newStats = {
      total: updatedOrders.length,
      pending: updatedOrders.filter((o) => o.status === "pending").length,
      processing: updatedOrders.filter((o) => o.status === "processing").length,
      shipped: updatedOrders.filter((o) => o.status === "shipped").length,
      delivered: updatedOrders.filter((o) => o.status === "delivered").length,
      cancelled: updatedOrders.filter((o) => o.status === "cancelled").length,
    };
    setStats(newStats);
  };

  // Handle export orders
  const handleExportOrders = () => {
    setIsExporting(true);

    try {
      // Create CSV content
      const csvHeaders = [
        "Order Number",
        "Customer Name",
        "Email",
        "Date",
        "Status",
        "Amount",
      ];
      const csvRows = filteredOrders.map((order) => [
        order.orderNumber,
        order.customer.name,
        order.customer.email,
        order.date,
        order.status,
        order.amount,
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      // Download CSV file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `orders_${new Date().toISOString().split("T")[0]}.csv`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Export Complete",
        description: "Orders have been exported to CSV",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export orders",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "processing":
        return "bg-blue-100 text-blue-700";
      case "shipped":
        return "bg-purple-100 text-purple-700";
      case "delivered":
        return "bg-green-100 text-green-700";
      case "cancelled":
        return "bg-red-100 text-red-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setCategoryFilter("all");
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Order Management
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage customer orders and fulfillment
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
          <Button
            variant="outline"
            onClick={handleExportOrders}
            disabled={isExporting}
            className="text-base px-6 py-3"
          >
            <Download className="w-5 h-5 mr-2" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
        </div>
      </div>

      {/* Order Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {[
          {
            title: "Total Orders",
            value: stats.total.toString(),
            color: "text-gray-900",
          },
          {
            title: "Pending",
            value: stats.pending.toString(),
            color: "text-orange-600",
          },
          {
            title: "Processing",
            value: stats.processing.toString(),
            color: "text-blue-600",
          },
          {
            title: "Shipped",
            value: stats.shipped.toString(),
            color: "text-purple-600",
          },
          {
            title: "Delivered",
            value: stats.delivered.toString(),
            color: "text-green-600",
          },
        ].map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={loading ? "..." : stat.value}
                  className="text-4xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search & Filter Orders */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <Search className="w-6 h-6" />
                <span className="font-medium text-xl">
                  Search & Filter Orders
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-lg px-6 py-3"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Orders
                </label>
                <Input
                  placeholder="Search by order ID, customer name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-lg"
                />
              </div>
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-lg">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-lg">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="health-drinks">Health Drinks</SelectItem>
                    <SelectItem value="supplements">Supplements</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-lg text-gray-500 dark:text-gray-400">
              Showing {filteredOrders.length} of {orders.length} orders
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
              Orders
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Order</div>
              <div className="text-center">Customer</div>
              <div className="text-center">Date</div>
              <div className="text-center">Items</div>
              <div className="text-center">Amount</div>
              <div className="text-center">Status</div>
              <div className="text-center">Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {loading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
                  >
                    {Array.from({ length: 7 }).map((_, colIndex) => (
                      <div key={colIndex} className="text-center">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                ))
              ) : filteredOrders.length === 0 ? (
                // Empty state
                <div className="text-center py-12">
                  <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No orders found
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    {searchTerm || statusFilter !== "all"
                      ? "Try adjusting your search or filters"
                      : "No orders have been placed yet"}
                  </p>
                </div>
              ) : (
                // Order data
                filteredOrders.map((order, index) => (
                  <div
                    key={order.id || index}
                    className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out"
                  >
                    <div className="text-center">
                      <AnimatedText className="font-medium text-lg text-black dark:text-white">
                        {order.orderNumber}
                      </AnimatedText>
                      <div className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                        {order.date}
                      </div>
                    </div>
                    <div className="text-center">
                      <AnimatedText className="font-medium text-lg">
                        {order.customer.name}
                      </AnimatedText>
                      <div className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                        {order.customer.email}
                      </div>
                    </div>
                    <div className="transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300 text-lg text-center">
                      {order.date}
                    </div>
                    <div className="transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300 text-lg text-center">
                      {order.items.length} items
                    </div>
                    <div className="text-center">
                      <AnimatedText className="font-medium text-lg">
                        {order.amount}
                      </AnimatedText>
                    </div>
                    <div className="flex justify-center">
                      <Badge
                        className={`${getStatusBadgeColor(
                          order.status
                        )} text-base`}
                      >
                        {order.status.charAt(0).toUpperCase() +
                          order.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewOrder(order)}
                        className="p-3"
                      >
                        <Eye className="w-6 h-6" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteOrder(order)}
                        className="p-3"
                      >
                        <Trash2 className="w-6 h-6" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <EnhancedOrderViewModal
        isOpen={isOrderViewModalOpen}
        onClose={() => setIsOrderViewModalOpen(false)}
        order={selectedOrder}
      />
    </div>
  );
};

export default Orders;
