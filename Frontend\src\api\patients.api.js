// Patients API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class PatientsAPI {
  // Get all patients with optional filters
  async getPatients(filters = {}) {
    try {
      const response = await serviceWorkerAPI.getPatients(filters);
      console.log("Patients fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch patients:", error);
      throw error;
    }
  }

  // Get patient by ID
  async getPatientById(id) {
    try {
      const response = await serviceWorkerAPI.getPatientById(id);
      console.log("Patient fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch patient:", error);
      throw error;
    }
  }

  // Create new patient
  async createPatient(patientData) {
    try {
      const response = await serviceWorkerAPI.createPatient(patientData);
      console.log("Patient created successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to create patient:", error);
      throw error;
    }
  }

  // Update patient
  async updatePatient(id, patientData) {
    try {
      const response = await serviceWorkerAPI.updatePatient(id, patientData);
      console.log("Patient updated successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to update patient:", error);
      throw error;
    }
  }

  // Delete patient
  async deletePatient(id) {
    try {
      const response = await serviceWorkerAPI.deletePatient(id);
      console.log("Patient deleted successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to delete patient:", error);
      throw error;
    }
  }

  // Get patient statistics
  async getPatientStats() {
    try {
      const response = await serviceWorkerAPI.getPatientStats();
      console.log("Patient stats fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch patient stats:", error);
      throw error;
    }
  }

  // Get patients by gender
  async getPatientsByGender(gender, filters = {}) {
    try {
      const allFilters = { ...filters, gender };
      const response = await this.getPatients(allFilters);
      console.log(
        `Patients with gender '${gender}' fetched successfully:`,
        response
      );
      return response;
    } catch (error) {
      console.error(`Failed to fetch patients with gender '${gender}':`, error);
      throw error;
    }
  }

  // Generate a unique 5-character Patient ID
  generatePatientId() {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 5; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Format patient data for display
  formatPatientForDisplay(patient) {
    if (!patient) return null;

    return {
      id: patient._id,
      patientId: patient.patientId || this.generatePatientId(),
      name: patient.name || "Unknown Patient",
      age: patient.age || 0,
      gender: patient.gender || "Not specified",
      contact: patient.contactNumber || "",
      address: patient.address || "",
      healthConcerns:
        patient.healthConditions
          ?.map((condition) => condition.condition)
          .join(", ") || "None specified",
      describeConcern:
        patient.decription || patient.description || "No description provided",
      appointmentDate: patient.nextDueAppointment
        ? new Date(patient.nextDueAppointment).toLocaleDateString()
        : "Not scheduled",
      prescriptionGiven: patient.appointmentDetails?.some(
        (apt) => apt.prescriptionGiven
      )
        ? "Yes"
        : "No",
      status: patient.status || "Active",
      createdAt: patient.createdAt,
      updatedAt: patient.updatedAt,
      // Additional fields for compatibility
      healthConcern:
        patient.healthConditions?.[0]?.condition || "General consultation",
    };
  }

  // Export patients data to CSV
  async exportPatientsToCSV(filters = {}) {
    try {
      const response = await this.getPatients(filters);
      const patients = response.patients || response.data || [];

      const csvData = patients.map((patient) => {
        const formatted = this.formatPatientForDisplay(patient);
        return {
          "Patient ID": formatted.patientId,
          Name: formatted.name,
          Age: formatted.age,
          Gender: formatted.gender,
          Contact: formatted.contact,
          Address: formatted.address,
          "Health Concerns": formatted.healthConcerns,
          Description: formatted.describeConcern,
          "Next Appointment": formatted.appointmentDate,
          "Prescription Given": formatted.prescriptionGiven,
          Status: formatted.status,
          "Registration Date": new Date(
            formatted.createdAt
          ).toLocaleDateString(),
        };
      });

      return csvData;
    } catch (error) {
      console.error("Failed to export patients:", error);
      throw error;
    }
  }

  // Schedule appointment for patient
  async scheduleAppointment(patientId, appointmentData) {
    try {
      const response = await serviceWorkerAPI.schedulePatientAppointment(
        patientId,
        appointmentData
      );
      console.log("Appointment scheduled successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to schedule appointment:", error);
      throw error;
    }
  }

  // Add health condition to patient
  async addHealthCondition(patientId, conditionData) {
    try {
      const response = await serviceWorkerAPI.addPatientHealthCondition(
        patientId,
        conditionData
      );
      console.log("Health condition added successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to add health condition:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const patientsAPI = new PatientsAPI();
export default patientsAPI;
