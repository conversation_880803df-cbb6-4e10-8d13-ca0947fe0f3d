import React from "react";
import { Loader2 } from "lucide-react";

interface LoadingStateProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  fullScreen?: boolean;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  message = "Loading...",
  size = "md",
  className = "",
  fullScreen = false,
}) => {
  const sizeClasses = {
    sm: {
      spinner: "w-4 h-4",
      text: "text-sm",
      container: "py-4",
    },
    md: {
      spinner: "w-6 h-6",
      text: "text-base",
      container: "py-8",
    },
    lg: {
      spinner: "w-8 h-8",
      text: "text-lg",
      container: "py-12",
    },
  };

  const currentSize = sizeClasses[size];

  const content = (
    <div className={`flex flex-col items-center justify-center ${currentSize.container} ${className}`}>
      <Loader2 className={`${currentSize.spinner} animate-spin text-blue-600 dark:text-blue-400 mb-3`} />
      <p className={`${currentSize.text} text-gray-600 dark:text-gray-300`}>
        {message}
      </p>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
};

// Table loading skeleton component
export const TableLoadingSkeleton: React.FC<{ rows?: number; columns?: number }> = ({
  rows = 5,
  columns = 4,
}) => {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="grid gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

// Card loading skeleton component
export const CardLoadingSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="p-6 border border-gray-200 dark:border-gray-600 rounded-lg space-y-4"
        >
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
          </div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      ))}
    </div>
  );
};

export default LoadingState;
