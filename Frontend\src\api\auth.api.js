// Authentication API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class AuthAPI {
  // Admin login - Step 1: Validate credentials and send 2FA code
  async loginAdmin(credentials) {
    try {
      const response = await serviceWorkerAPI.loginAdmin(credentials);
      console.log("Admin login successful:", response);
      return response;
    } catch (error) {
      console.error("Admin login failed:", error);
      throw error;
    }
  }

  // Employee login - Step 1: Validate credentials and send 2FA code
  async loginEmployee(credentials) {
    try {
      const response = await serviceWorkerAPI.loginEmployee(credentials);
      console.log("Employee login successful:", response);
      return response;
    } catch (error) {
      console.error("Employee login failed:", error);
      throw error;
    }
  }

  // Two-factor verification - Step 2: Verify code and get JWT token
  async verifyTwoFactor(data) {
    try {
      const response = await serviceWorkerAPI.verifyTwoFactor(data);
      console.log("Two-factor verification successful:", response);

      // Note: Token storage is handled by TwoFactorAuth component
      // based on "Keep me logged in" preference

      return response;
    } catch (error) {
      console.error("Two-factor verification failed:", error);
      throw error;
    }
  }

  // Register new user
  async register(userData) {
    try {
      const response = await serviceWorkerAPI.register(userData);
      console.log("Registration successful:", response);
      return response;
    } catch (error) {
      console.error("Registration failed:", error);
      throw error;
    }
  }

  // Forgot password
  async forgotPassword(email) {
    try {
      const response = await serviceWorkerAPI.forgotPassword(email);
      console.log("Forgot password request sent:", response);
      return response;
    } catch (error) {
      console.error("Forgot password failed:", error);
      throw error;
    }
  }

  // Reset password
  async resetPassword(token, password) {
    try {
      const response = await serviceWorkerAPI.resetPassword(token, password);
      console.log("Password reset successful:", response);
      return response;
    } catch (error) {
      console.error("Password reset failed:", error);
      throw error;
    }
  }

  // Logout user
  logout() {
    try {
      // Clear stored auth data from both storages
      localStorage.removeItem("auth-token");
      localStorage.removeItem("auth-user");
      localStorage.removeItem("keep-logged-in");
      sessionStorage.removeItem("auth-token");
      sessionStorage.removeItem("auth-user");
      sessionStorage.removeItem("keep-logged-in");

      console.log("User logged out successfully");

      // Dispatch logout event
      window.dispatchEvent(new CustomEvent("auth-logout"));

      return { success: true, message: "Logged out successfully" };
    } catch (error) {
      console.error("Logout error:", error);
      throw error;
    }
  }

  // Get current user from localStorage or sessionStorage
  getCurrentUser() {
    try {
      const userData =
        localStorage.getItem("auth-user") ||
        sessionStorage.getItem("auth-user");
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Error getting current user:", error);
      return null;
    }
  }

  // Get auth token from localStorage or sessionStorage
  getToken() {
    return (
      localStorage.getItem("auth-token") || sessionStorage.getItem("auth-token")
    );
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getToken();
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  // Check if user has specific role
  hasRole(role) {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  // Check if user is admin
  isAdmin() {
    return this.hasRole("admin");
  }

  // Check if user is employee
  isEmployee() {
    return this.hasRole("employee");
  }

  // Update user data in localStorage or sessionStorage
  updateUser(userData) {
    try {
      const currentUser = this.getCurrentUser();
      if (currentUser) {
        const updatedUser = { ...currentUser, ...userData };

        // Update in the same storage where the user data exists
        if (localStorage.getItem("auth-user")) {
          localStorage.setItem("auth-user", JSON.stringify(updatedUser));
        } else if (sessionStorage.getItem("auth-user")) {
          sessionStorage.setItem("auth-user", JSON.stringify(updatedUser));
        }

        // Dispatch user update event
        window.dispatchEvent(
          new CustomEvent("auth-user-updated", {
            detail: { user: updatedUser },
          })
        );

        return updatedUser;
      }
      return null;
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  // Verify if token is still valid (optional - can be used for token refresh)
  async verifyToken() {
    try {
      const response = await serviceWorkerAPI.getUserProfile();
      return response.success;
    } catch (error) {
      // Token is invalid or expired
      this.logout();
      return false;
    }
  }

  // Set password for invited staff member
  async setStaffPassword(passwordData) {
    try {
      const response = await serviceWorkerAPI.setStaffPassword(passwordData);
      console.log("Staff password set successfully:", response);

      // Store token if provided
      if (response.success && response.data?.token) {
        localStorage.setItem('token', response.data.token);
      }

      return response;
    } catch (error) {
      console.error("Failed to set staff password:", error);
      throw error;
    }
  }
}

// Create and export singleton instance
const authAPI = new AuthAPI();
export default authAPI;

// Also export the class
export { AuthAPI };
