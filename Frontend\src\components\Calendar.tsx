import React, { useState, useRef, useEffect } from "react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  CalendarDays,
  Image,
  X,
  Upload,
  Eye,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import ImagePreviewModal from "./ImagePreviewModal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import eventsAPI from "@/api/events.api.js";
import { apiHelpers } from "@/utils/apiHelpers";

interface BannerEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  imageUrl?: string;
  priority: "Level 1" | "Level 2" | "Level 3";
  link?: string;
  validFrom: Date;
  validTill: Date;
}

const Calendar = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [events, setEvents] = useState<BannerEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string>("");
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [bannerForm, setBannerForm] = useState({
    title: "",
    description: "",
    imageUrl: "",
    priority: "Level 2" as "Level 1" | "Level 2" | "Level 3",
    link: "",
    validFrom: "",
    validTill: "",
  });
  const { toast } = useToast();

  // Fetch banners from backend
  const fetchBanners = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const response = await eventsAPI.getEvents();

      if (apiHelpers.isSuccessResponse(response)) {
        const data = apiHelpers.extractData(response);
        const bannersData = data.banners || [];

        // Format banners for display
        const formattedBanners = bannersData.map((banner: any) =>
          eventsAPI.formatEventForDisplay(banner)
        );

        setEvents(formattedBanners);
        console.log("Banners loaded:", formattedBanners);
      } else {
        throw new Error(response.message || "Failed to fetch banners");
      }
    } catch (error: any) {
      console.error("Failed to fetch banners:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh banners
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchBanners(false);
  };

  // Load banners on component mount
  useEffect(() => {
    fetchBanners();
  }, []);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        setImagePreview(result);
        setBannerForm({ ...bannerForm, imageUrl: result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setBannerForm({ ...bannerForm, imageUrl: url });

    if (url) {
      setImagePreview(url);
    } else {
      setImagePreview(null);
    }
  };

  const handleCreateBanner = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!date) {
      toast({
        title: "Error",
        description: "Please select a date for the banner",
        variant: "destructive",
      });
      return;
    }

    if (!bannerForm.validFrom || !bannerForm.validTill) {
      toast({
        title: "Error",
        description: "Please select valid from and valid till dates",
        variant: "destructive",
      });
      return;
    }

    const validFromDate = new Date(bannerForm.validFrom);
    const validTillDate = new Date(bannerForm.validTill);

    if (validFromDate >= validTillDate) {
      toast({
        title: "Error",
        description: "Valid till date must be after valid from date",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsCreating(true);

      const bannerData = {
        title: bannerForm.title,
        description: bannerForm.description,
        date: date.toISOString(),
        imageUrl: bannerForm.imageUrl,
        priority: bannerForm.priority,
        link: bannerForm.link,
        validFrom: validFromDate.toISOString(),
        validTill: validTillDate.toISOString(),
      };

      const response = await eventsAPI.createEvent(bannerData);

      if (apiHelpers.isSuccessResponse(response)) {
        toast({
          title: "Banner created!",
          description: `Banner scheduled with ${bannerForm.priority} priority`,
        });

        // Reset form
        setBannerForm({
          title: "",
          description: "",
          imageUrl: "",
          priority: "Level 2",
          link: "",
          validFrom: "",
          validTill: "",
        });
        setImagePreview(null);
        setIsModalOpen(false);

        // Refresh banners list
        await fetchBanners(false);
      } else {
        throw new Error(response.message || "Failed to create banner");
      }
    } catch (error: any) {
      console.error("Failed to create banner:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const removeImagePreview = () => {
    setImagePreview(null);
    setBannerForm({ ...bannerForm, imageUrl: "" });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openImagePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setIsPreviewOpen(true);
  };

  const getEventsForDate = (selectedDate: Date) => {
    return events.filter(
      (event) => event.date.toDateString() === selectedDate.toDateString()
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Level 1":
        return "bg-red-100 text-red-800 border-red-200";
      case "Level 2":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Level 3":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Calendar
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage banners and promotional events
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="transition-all duration-300 ease-in-out"
          >
            {isRefreshing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Refresh
          </Button>

          <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700 transition-all duration-300 ease-in-out">
                <Plus className="w-4 h-4 mr-2" />
                Create Banner
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Banner</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateBanner} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Banner Title</Label>
                  <Input
                    id="title"
                    value={bannerForm.title}
                    onChange={(e) =>
                      setBannerForm({ ...bannerForm, title: e.target.value })
                    }
                    placeholder="Enter banner title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={bannerForm.description}
                    onChange={(e) =>
                      setBannerForm({
                        ...bannerForm,
                        description: e.target.value,
                      })
                    }
                    placeholder="Enter banner description"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="link">Link (URL)</Label>
                  <Input
                    id="link"
                    type="url"
                    value={bannerForm.link}
                    onChange={(e) =>
                      setBannerForm({ ...bannerForm, link: e.target.value })
                    }
                    placeholder="https://example.com"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="validFrom">Valid From (Start Date)</Label>
                    <Input
                      id="validFrom"
                      type="date"
                      value={bannerForm.validFrom}
                      onChange={(e) =>
                        setBannerForm({
                          ...bannerForm,
                          validFrom: e.target.value,
                        })
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="validTill">Valid Till (End Date)</Label>
                    <Input
                      id="validTill"
                      type="date"
                      value={bannerForm.validTill}
                      onChange={(e) =>
                        setBannerForm({
                          ...bannerForm,
                          validTill: e.target.value,
                        })
                      }
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Banner Image</Label>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="flex items-center gap-2 transition-all duration-300 ease-in-out"
                    >
                      <Upload className="w-4 h-4" />
                      Upload Image
                    </Button>
                    <Input
                      placeholder="Or paste image URL"
                      value={bannerForm.imageUrl}
                      onChange={handleImageUrlChange}
                      className="flex-1"
                    />
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>

                {/* Image Preview Section */}
                {imagePreview && (
                  <div className="space-y-2">
                    <Label>Image Preview</Label>
                    <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800 transition-all duration-300 ease-in-out">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-green-600 dark:text-green-400 flex items-center gap-2">
                          <Image className="w-4 h-4" />
                          Image Attached
                        </span>
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => openImagePreview(imagePreview)}
                            className="transition-all duration-300 ease-in-out"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={removeImagePreview}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-300 ease-in-out"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center overflow-hidden">
                        <img
                          src={imagePreview}
                          alt="Banner preview"
                          className="max-w-full max-h-full object-contain"
                          onError={() => {
                            toast({
                              title: "Invalid Image",
                              description: "Unable to load image",
                              variant: "destructive",
                            });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="priority">Banner Priority Level</Label>
                  <Select
                    value={bannerForm.priority}
                    onValueChange={(value: "Level 1" | "Level 2" | "Level 3") =>
                      setBannerForm({ ...bannerForm, priority: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Level 1">
                        Level 1 - High Priority
                      </SelectItem>
                      <SelectItem value="Level 2">
                        Level 2 - Medium Priority
                      </SelectItem>
                      <SelectItem value="Level 3">
                        Level 3 - Low Priority
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsModalOpen(false);
                      setImagePreview(null);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isCreating}
                    className="bg-blue-600 hover:bg-blue-700 transition-all duration-300 ease-in-out"
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Banner"
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
              <CalendarDays className="w-5 h-5" />
              Calendar View
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CalendarComponent
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border w-full"
            />
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
              Events for {date?.toLocaleDateString()}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {date && getEventsForDate(date).length > 0 ? (
                getEventsForDate(date).map((event) => (
                  <div
                    key={event.id}
                    className="p-3 border rounded-lg transition-all duration-300 ease-in-out hover:shadow-md bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {event.title}
                      </h4>
                      <span
                        className={`text-base px-2 py-1 rounded-full border ${getPriorityColor(
                          event.priority
                        )}`}
                      >
                        {event.priority}
                      </span>
                    </div>
                    <p className="text-base text-gray-600 dark:text-gray-400 mb-2 transition-colors duration-500 ease-in-out">
                      {event.description}
                    </p>
                    {event.imageUrl && (
                      <button
                        onClick={() => openImagePreview(event.imageUrl!)}
                        className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-700 transition-colors duration-300 ease-in-out"
                      >
                        <Image className="w-3 h-3" />
                        Image Attached
                      </button>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-sm">
                  No banners scheduled for this date
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            All Scheduled Banners
          </CardTitle>
        </CardHeader>
        <CardContent>
          {events.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {events.map((event) => (
                <div
                  key={event.id}
                  className="border rounded-lg p-4 transition-all duration-300 ease-in-out hover:shadow-md bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      {event.title}
                    </h3>
                    <span
                      className={`text-base px-2 py-1 rounded-full border ${getPriorityColor(
                        event.priority
                      )}`}
                    >
                      {event.priority}
                    </span>
                  </div>
                  <p className="text-base text-gray-600 dark:text-gray-400 mb-2 transition-colors duration-500 ease-in-out">
                    {event.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                      Scheduled: {event.date.toLocaleDateString()}
                    </div>
                    {event.imageUrl && (
                      <button
                        onClick={() => openImagePreview(event.imageUrl!)}
                        className="text-xs text-blue-600 hover:text-blue-700 transition-colors duration-300 ease-in-out flex items-center gap-1"
                      >
                        <Eye className="w-3 h-3" />
                        Preview
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">
              No banners created yet. Click "Create Banner" to get started.
            </p>
          )}
        </CardContent>
      </Card>

      <ImagePreviewModal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        imageUrl={previewImage}
        title="Banner Image Preview"
      />
    </div>
  );
};

export default Calendar;
