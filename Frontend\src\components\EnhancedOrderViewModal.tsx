import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  MapPin,
  User,
  Package,
  Calendar,
  Truck,
  Clock,
  CheckCircle,
} from "lucide-react";

interface EnhancedOrderViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: any;
}

const EnhancedOrderViewModal = ({
  isOpen,
  onClose,
  order,
}: EnhancedOrderViewModalProps) => {
  if (!order) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processing":
        return "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300";
      case "shipped":
        return "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300";
      case "delivered":
        return "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300";
      case "cancelled":
        return "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  // Get tracking information from order data
  const getTrackingInfo = () => {
    if (!order.trackingNumber) return null;

    return {
      trackingNumber: order.trackingNumber,
      carrier: order.carrier || "Shiprocket",
      estimatedDelivery: order.estimatedDelivery || "2-3 business days",
      currentStatus: order.status,
      trackingSteps: [
        { status: "Order Confirmed", completed: true, date: order.date },
        {
          status: "Processing",
          completed: order.status !== "pending",
          date:
            order.status !== "pending" ? order.updatedAt || order.date : null,
        },
        {
          status: "Shipped",
          completed: ["shipped", "delivered"].includes(order.status),
          date:
            order.status === "shipped" || order.status === "delivered"
              ? order.updatedAt
              : null,
        },
        {
          status: "Delivered",
          completed: order.status === "delivered",
          date: order.status === "delivered" ? order.updatedAt : null,
        },
      ],
    };
  };

  const trackingInfo = getTrackingInfo();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-gray-900 dark:text-white">
              Order Details - {order.orderNumber}
            </DialogTitle>
            <Badge className={getStatusColor(order.status)}>
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer Information */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <User className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Customer Information
              </h3>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Name</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {order.customer.name}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Email
                </p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {order.customer.email}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Order Date
                </p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {order.date}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Order ID
                </p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {order.orderNumber}
                </p>
              </div>
            </div>
          </div>

          {/* Shiprocket Tracking Details */}
          {trackingInfo && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-4">
                <Truck className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Tracking Information
                </h3>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Tracking Number
                  </p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {trackingInfo.trackingNumber}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Carrier
                  </p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {trackingInfo.carrier}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Estimated Delivery
                  </p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {trackingInfo.estimatedDelivery}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Current Status
                  </p>
                  <Badge className={getStatusColor(trackingInfo.currentStatus)}>
                    {trackingInfo.currentStatus.charAt(0).toUpperCase() +
                      trackingInfo.currentStatus.slice(1)}
                  </Badge>
                </div>
              </div>

              {/* Tracking Steps */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Tracking Progress
                </h4>
                <div className="space-y-2">
                  {trackingInfo.trackingSteps.map((step, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div
                        className={`w-4 h-4 rounded-full flex items-center justify-center ${
                          step.completed
                            ? "bg-green-500 text-white"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        {step.completed && <CheckCircle className="w-3 h-3" />}
                      </div>
                      <div className="flex-1">
                        <p
                          className={`font-medium ${
                            step.completed
                              ? "text-gray-900 dark:text-white"
                              : "text-gray-500 dark:text-gray-400"
                          }`}
                        >
                          {step.status}
                        </p>
                        {step.date && (
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(step.date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <Separator className="bg-gray-200 dark:bg-gray-600" />

          {/* Order Items */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Package className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Order Items
              </h3>
            </div>
            <div className="space-y-3">
              {order.items.map((item: any, index: number) => (
                <div
                  key={index}
                  className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                >
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 dark:text-white">
                      {item.name}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      SKU: {item.sku}
                    </p>
                  </div>
                  <div className="text-center px-4">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Qty
                    </p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {item.quantity}
                    </p>
                  </div>
                  <div className="text-center px-4">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Price
                    </p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      ₹{item.price}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Total
                    </p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      ₹{item.price * item.quantity}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Order Summary
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">
                  Subtotal
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ₹{order.subtotal}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">
                  Shipping
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ₹{order.shipping}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Tax</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ₹{order.tax}
                </span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t border-gray-200 dark:border-gray-600 pt-2">
                <span className="text-gray-900 dark:text-white">Total</span>
                <span className="text-green-600 dark:text-green-400">
                  {order.amount}
                </span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EnhancedOrderViewModal;
