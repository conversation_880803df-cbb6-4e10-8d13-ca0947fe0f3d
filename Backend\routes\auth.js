const express = require('express');
const router = express.Router();
const {
    registerUser,
    loginAdmin,
    loginEmployee,
    verifyTwoFactorCode,
    verifyEmail,
    forgotPassword,
    resetPassword
} = require('../controllers/userController');

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
router.post('/register', registerUser);

// @desc    Admin Login - Step 1: Validate credentials and send 2FA code
// @route   POST /api/auth/login-admin
// @access  Public
router.post('/login-admin', loginAdmin);

// @desc    Employee Login - Step 1: Validate credentials and send 2FA code
// @route   POST /api/auth/login-employee
// @access  Public
router.post('/login-employee', loginEmployee);

// @desc    Two-Factor Verification - Step 2: Verify code and issue JWT token
// @route   POST /api/auth/verify-code
// @access  Public
router.post('/verify-code', verifyTwoFactorCode);

// @desc    Verify email
// @route   GET /api/auth/verify-email/:token
// @access  Public
router.get('/verify-email/:token', verifyEmail);

// @desc    Forgot password
// @route   POST /api/auth/forgot-password
// @access  Public
router.post('/forgot-password', forgotPassword);

// @desc    Reset password
// @route   POST /api/auth/reset-password/:token
// @access  Public
router.post('/reset-password/:token', resetPassword);

module.exports = router;
